#pragma once

#include <QString>

namespace ChamberUI::Services {

class ProviderUtility {
public:
  constexpr static QString getOpenAIDefaultApiUrl() {
    return "https://api.openai.com/v1/chat/completions";
  }
  constexpr static QString getAnthropicDefaultApiUrl() {
    return "https://api.anthropic.com/api/v1/claude/messages";
  }
  constexpr static QString getOpenAIDefaultModel() { return "gpt-4o"; }
  constexpr static QString getAnthropicDefaultModel() {
    return "claude-3-opus-20240229";
  }
  constexpr static double getOpenAIDefaultTemperature() { return 0.7f; }
  constexpr static double getAnthropicDefaultTemperature() { return 0.7f; }
  constexpr static int getOpenAIDefaultMaxTokens() { return 2000; }
  constexpr static int getAnthropicDefaultMaxTokens() { return 2000; }
};

} // namespace ChamberUI::Services
