#include "openai_provider.h"

#include "api_provider.h"

#include <QDebug>
#include <QNetworkRequest>
#include <QUrlQuery>

#include "models/conversation.h"
#include "models/conversation_settings.h"
#include "provider_utility.h"

namespace ChamberUI::Services {

OpenAIProvider::OpenAIProvider(gsl::not_null<APIProvider *> provider_settings,
                               gsl::not_null<QObject *> parent)
    : Provider{parent}, settings_{provider_settings},
      network_manager_{new QNetworkAccessManager{this}} {
  settings_->setParent(this);
  if (settings_->apiUrl().isEmpty()) {
    settings_->setApiUrl(ProviderUtility::getOpenAIDefaultApiUrl());
  }
  if (settings_->model().isEmpty()) {
    settings_->setModel(ProviderUtility::getOpenAIDefaultModel());
  }
  if (settings_->temperature() < 0.0 || settings_->temperature() > 1.0) {
    settings_->setTemperature(ProviderUtility::getOpenAIDefaultTemperature());
  }
  if (settings_->maxTokens() <= 0) {
    settings_->setMaxTokens(ProviderUtility::getOpenAIDefaultMaxTokens());
  }
  connect(network_manager_, &QNetworkAccessManager::finished, this,
          &OpenAIProvider::handleNetworkReply);
}

OpenAIProvider::~OpenAIProvider() {
  // Cancel any pending requests
  for (auto reply : active_requests_.keys()) {
    reply->abort();
  }
}

QString OpenAIProvider::name() const { return "openai"; }

QString OpenAIProvider::displayName() const { return "OpenAI"; }

bool OpenAIProvider::requiresApiKey() const { return true; }

APIProvider &OpenAIProvider::mutableSettings() { return *settings_; }
const APIProvider &OpenAIProvider::settings() const { return *settings_; }

QStringList OpenAIProvider::availableModels() const {
  return QStringList() << "gpt-4o"
                       << "gpt-4-turbo"
                       << "gpt-4"
                       << "gpt-3.5-turbo";
}

void OpenAIProvider::processMessage(
    gsl::not_null<const Conversation *> conversation,
    gsl::not_null<const ConversationSettings *> conversation_settings) {
  if (settings_->key().isEmpty()) {
    emit errorOccurred(
        conversation->id(),
        "API key is not set. Please set your OpenAI API key in the settings.");
    return;
  }

  emit processingStarted(conversation->id());

  // Prepare the request
  QNetworkRequest request(QUrl(settings_->apiUrl()));
  request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
  request.setRawHeader("Authorization",
                       QString("Bearer %1").arg(settings_->key()).toUtf8());

  // Prepare the request body
  QJsonObject requestObj;
  QJsonArray messages;
  ProviderUtility::formatOpenAIFormatMessages(conversation, messages);
  requestObj["model"] = conversation_settings->model.isEmpty()
                            ? settings_->model()
                            : conversation_settings->model;
  requestObj["messages"] = std::move(messages);
  requestObj["temperature"] = conversation_settings->temperature;
  requestObj["max_tokens"] = conversation_settings->max_tokens;

  QJsonDocument requestDoc(requestObj);
  QByteArray requestData = requestDoc.toJson();

  // Send the request
  QNetworkReply *reply = network_manager_->post(request, requestData);
}

void OpenAIProvider::processMessage(const QString &conversation_id,
                                    const QVariantList &history,
                                    const QString &message,
                                    const QVariantMap &conversation_settings) {
  // Check if API key is set
  if (settings_["apiKey"].toString().isEmpty()) {
    emit errorOccurred(
        conversation_id,
        "API key is not set. Please set your OpenAI API key in the settings.");
    return;
  }

  emit processingStarted(conversation_id);

  // Prepare the request
  QNetworkRequest request(QUrl("https://api.openai.com/v1/chat/completions"));
  request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
  request.setRawHeader(
      "Authorization",
      QString("Bearer %1").arg(settings_["apiKey"].toString()).toUtf8());

  // Prepare the request body
  QJsonObject requestObj;

  // Use conversation-specific model if provided, otherwise use the default
  if (conversation_settings.contains("model") &&
      !conversation_settings["model"].toString().isEmpty()) {
    requestObj["model"] = conversation_settings["model"].toString();
  } else {
    requestObj["model"] = settings_["model"].toString();
  }

  // Use conversation-specific system prompt if provided
  QVariantList historyWithSystemPrompt = history;
  if (conversation_settings.contains("systemPrompt") &&
      !conversation_settings["systemPrompt"].toString().isEmpty()) {
    // Remove any existing system messages
    for (int i = historyWithSystemPrompt.size() - 1; i >= 0; i--) {
      QVariantMap msg = historyWithSystemPrompt[i].toMap();
      if (msg["role"].toString() == "system") {
        historyWithSystemPrompt.removeAt(i);
      }
    }

    // Add the conversation-specific system prompt
    QVariantMap systemMsg;
    systemMsg["role"] = "system";
    systemMsg["content"] = conversation_settings["systemPrompt"].toString();
    historyWithSystemPrompt.prepend(systemMsg);
  }

  requestObj["messages"] = formatMessages(historyWithSystemPrompt, message);

  // Use conversation-specific temperature if provided, otherwise use the
  // default
  if (conversation_settings.contains("temperature")) {
    requestObj["temperature"] = conversation_settings["temperature"].toDouble();
  } else {
    requestObj["temperature"] = settings_["temperature"].toDouble();
  }

  // Use conversation-specific max tokens if provided, otherwise use the default
  if (conversation_settings.contains("maxTokens") &&
      conversation_settings["maxTokens"].toInt() > 0) {
    requestObj["max_tokens"] = conversation_settings["maxTokens"].toInt();
  } else if (settings_.contains("maxTokens") &&
             settings_["maxTokens"].toInt() > 0) {
    requestObj["max_tokens"] = settings_["maxTokens"].toInt();
  }

  // Add any additional settings from the conversation
  if (conversation_settings.contains("settings")) {
    QVariantMap additionalSettings = conversation_settings["settings"].toMap();
    for (auto it = additionalSettings.constBegin();
         it != additionalSettings.constEnd(); ++it) {
      // Skip settings we've already handled
      if (it.key() != "model" && it.key() != "temperature" &&
          it.key() != "maxTokens" && it.key() != "systemPrompt") {
        requestObj[it.key()] = QJsonValue::fromVariant(it.value());
      }
    }
  }

  QJsonDocument requestDoc(requestObj);
  QByteArray requestData = requestDoc.toJson();

  // Send the request
  QNetworkReply *reply = network_manager_->post(request, requestData);
  active_requests_[reply] = conversation_id;

  // Debug output
  qDebug() << "OpenAI request sent for conversation:" << conversation_id;
}

void OpenAIProvider::handleNetworkReply(QNetworkReply *reply) {
  // Get the conversation ID for this reply
  QString conversationId = active_requests_.value(reply);

  // Remove from active requests
  active_requests_.remove(reply);

  // Handle the reply
  if (reply->error() == QNetworkReply::NoError) {
    QByteArray responseData = reply->readAll();
    QJsonDocument responseDoc = QJsonDocument::fromJson(responseData);
    QJsonObject responseObj = responseDoc.object();

    // Extract the response content
    QString content = extractContent(responseObj);

    if (!content.isEmpty()) {
      emit responseReceived(conversationId, content);
    } else {
      emit errorOccurred(conversationId,
                         "Failed to extract content from OpenAI response");
    }
  } else {
    // Handle error
    QByteArray responseData = reply->readAll();
    QString errorMessage;

    // Try to parse error message from JSON response
    QJsonDocument errorDoc = QJsonDocument::fromJson(responseData);
    if (!errorDoc.isNull() && errorDoc.isObject()) {
      QJsonObject errorObj = errorDoc.object();
      if (errorObj.contains("error") && errorObj["error"].isObject()) {
        QJsonObject error = errorObj["error"].toObject();
        if (error.contains("message")) {
          errorMessage = error["message"].toString();
        }
      }
    }

    // If we couldn't parse the error message, use the reply's error string
    if (errorMessage.isEmpty()) {
      errorMessage = reply->errorString();
    }

    emit errorOccurred(conversationId,
                       QString("OpenAI API error: %1").arg(errorMessage));
  }

  // Signal that processing is finished
  emit processingFinished(conversationId);

  // Clean up
  reply->deleteLater();
}

QJsonArray OpenAIProvider::formatMessages(const QVariantList &history,
                                          const QString &message) {
  QJsonArray messages;

  // Add system message if not present in history
  bool hasSystemMessage = false;

  // Add history messages
  for (const QVariant &item : history) {
    QVariantMap messageItem = item.toMap();
    QString role = messageItem["role"].toString();

    if (role == "system") {
      hasSystemMessage = true;
    }

    QJsonObject messageObj;
    messageObj["role"] = role;
    messageObj["content"] = messageItem["content"].toString();
    messages.append(messageObj);
  }

  // Add default system message if none exists
  if (!hasSystemMessage) {
    QJsonObject systemMessage;
    systemMessage["role"] = "system";
    systemMessage["content"] = "You are a helpful assistant.";
    messages.prepend(systemMessage);
  }

  // Add the current user message
  QJsonObject userMessage;
  userMessage["role"] = "user";
  userMessage["content"] = message;
  messages.append(userMessage);

  return messages;
}

QString OpenAIProvider::extractContent(const QJsonObject &responseObj) {
  if (responseObj.contains("choices") && responseObj["choices"].isArray()) {
    QJsonArray choices = responseObj["choices"].toArray();

    if (!choices.isEmpty() && choices[0].isObject()) {
      QJsonObject choice = choices[0].toObject();

      if (choice.contains("message") && choice["message"].isObject()) {
        QJsonObject message = choice["message"].toObject();

        if (message.contains("content")) {
          return message["content"].toString();
        }
      }
    }
  }

  return QString();
}

} // namespace ChamberUI::Services