#pragma once

#include <gsl/gsl>

#include <QObject>
#include <QString>
#include <QVariantMap>

#include "models/types.h"

namespace ChamberUI::Services {

using namespace Models;

class Provider : public QObject {
  Q_OBJECT

public:
  explicit Provider(gsl::not_null<QObject *> parent) : QObject{parent} {}
  virtual ~Provider() = default;

  virtual QString name() const = 0;
  virtual QString displayName() const = 0;
  virtual bool requiresApiKey() const = 0;

  // virtual void setSettings(const QVariantMap &settings) = 0;
  // virtual QVariantMap settings() const = 0;

  virtual APIProvider &mutableSettings() = 0;
  [[nodiscard]]
  virtual const APIProvider &settings() const = 0;
  [[nodiscard]]
  virtual QStringList availableModels() const = 0;

  // virtual void
  // processMessage(const QString &conversationId, const QVariantList &history,
  //                const QString &message,
  //                const QVariantMap &conversationSettings = QVariantMap()) =
  //                0;

  virtual void processMessage(
      gsl::not_null<const Conversation *> conversation,
      gsl::not_null<const ConversationSettings *> conversation_settings) = 0;

signals:
  void responseReceived(const QString &conversationId, const QString &response);
  void errorOccurred(const QString &conversationId, const QString &error);
  void processingStarted(const QString &conversationId);
  void processingFinished(const QString &conversationId);
};

} // namespace ChamberUI::Services