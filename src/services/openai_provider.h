#pragma once

#include <gsl/gsl>

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QObject>
#include <QString>
#include <QVariantMap>

#include "provider.h"

#include "models/types.h"

namespace ChamberUI::Services {

class OpenAIProvider : public Provider {
  Q_OBJECT

public:
  OpenAIProvider(gsl::not_null<APIProvider *> provider_settings,
                 gsl::not_null<QObject *> parent);
  ~OpenAIProvider() final;

  QString name() const override;
  QString displayName() const override;
  bool requiresApiKey() const override;
  APIProvider &mutableSettings() override;
  const APIProvider &settings() const override;
  QStringList availableModels() const override;
  void processMessage(gsl::not_null<const Conversation *> conversation,
                      gsl::not_null<const ConversationSettings *>
                          conversation_settings) override;

private slots:
  void handleNetworkReply(QNetworkReply *reply);

private:
  gsl::owner<APIProvider *> settings_;
  QNetworkAccessManager *network_manager_;

  // Map to track ongoing requests by conversation ID
  QMap<QNetworkReply *, QString> active_requests_;

  // Helper methods
  QJsonArray formatMessages(const QVariantList &history,
                            const QString &message);
  QString extractContent(const QJsonObject &responseObj);

};

} // namespace ChamberUI::Services