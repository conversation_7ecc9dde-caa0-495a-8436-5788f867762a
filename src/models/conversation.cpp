#include "conversation.h"

#include <QUuid>

#include "message.h"

namespace ChamberUI::Models {

Conversation::Conversation(const QString &title, QObject *parent)
    : QObject{parent}, id_{QUuid::createUuid().toString(QUuid::WithoutBraces)},
      title_{title}, created_at_{QDateTime::currentDateTime()},
      updated_at_{QDateTime::currentDateTime()} {}

Conversation::Conversation(const QString &_id, const QString &_title,
                           QObject *parent)
    : QObject{parent}, id_(_id), title_(_title),
      created_at_(QDateTime::currentDateTime()),
      updated_at_(QDateTime::currentDateTime()) {}

// Messages QML属性实现
QQmlListProperty<Message> Conversation::messages() {
  return QQmlListProperty<Message>(
      this, nullptr, &Conversation::messages_append,
      &Conversation::messages_count, &Conversation::messages_at,
      &Conversation::messages_clear);
}

void Conversation::addMessage(Message *message) {
  if (message && !messages_.contains(message)) {
    message->setParent(this);                // 设置父对象管理生命周期
    message->setConversationId(id_.value()); // 自动设置会话ID
    messages_.append(message);
    updateMessageCount();
    updateTimestamp();
    emit messagesChanged();
  }
}

void Conversation::removeMessage(Message *message) {
  if (messages_.removeOne(message)) {
    // message->setParent(nullptr);
    updateMessageCount();
    updateTimestamp();
    emit messagesChanged();
  }
}

bool Conversation::removeMessage(const QString &message_id) {
  auto it = std::remove_if(
      messages_.begin(), messages_.end(),
      [message_id](Message *m) { return m->id() == message_id; });
  if (it != messages_.end()) {
    messages_.erase(it, messages_.end());
    updateMessageCount();
    updateTimestamp();
    emit messagesChanged();
    return true;
  }
  return false;
}

void Conversation::addMessagesIfEmpty(
    const QList<gsl::not_null<Message *>> &messages) {
  if (!messages.empty()) {
    return;
  }
  for (auto msg : messages) {
    msg->setParent(this);
    messages_.append(msg);
  }
  updateMessageCount();
  updateTimestamp();
  emit messagesChanged();
}

QList<gsl::not_null<const Message *>> Conversation::getHistory() const {
  QList<gsl::not_null<const Message *>> history;
  for (auto *message : messages_) {
    if (message->role() == Message::System) {
      continue;
    }
    history.append(gsl::make_not_null(message));
  }
  return history;
}

void Conversation::clearMessages() {
  if (!messages_.isEmpty()) {
    // for (auto *message : messages_) {
    //   message->setParent(nullptr);
    // }
    messages_.clear();
    updateMessageCount();
    updateTimestamp();
    emit messagesChanged();
  }
}

int Conversation::getMessageCount() const { return messages_.size(); }

Message *Conversation::messageAt(int index) const {
  if (index >= 0 && index < messages_.size()) {
    return messages_.at(index);
  }
  return nullptr;
}

Message *Conversation::lastMessage() const {
  return messages_.isEmpty() ? nullptr : messages_.last();
}

Message *Conversation::getMessage(const QString &message_id) const {
  auto it = std::find_if(
      messages_.begin(), messages_.end(),
      [&message_id](const Message *m) { return m->id() == message_id; });
  return (it != messages_.end()) ? *it : nullptr;
}

void Conversation::setMessages(const QList<Message *> &messages) {
  clearMessages();
  for (auto *message : messages) {
    addMessage(message);
  }
}

void Conversation::setSettingValue(const QString &key, const QVariant &value) {
  QVariantMap settingsMap = settings_.value();
  settingsMap[key] = value;
  setSettings(settingsMap);
}

QVariant Conversation::getSettingValue(const QString &key,
                                       const QVariant &defaultValue) const {
  return settings_.value().value(key, defaultValue);
}

void Conversation::markAsTemplate(const QString &templateName) {
  setIsTemplate(true);
  setTemplateName(templateName);
}

void Conversation::unmarkAsTemplate() {
  setIsTemplate(false);
  setTemplateName(QString());
}

Conversation *Conversation::createFromTemplate(const QString &newId,
                                               const QString &newTitle) const {
  if (!is_template_.value()) {
    return nullptr;
  }

  auto *newConversation = new Conversation(newId, newTitle);
  newConversation->setProvider(provider_.value());
  newConversation->setModel(model_.value());
  newConversation->setSystemPrompt(system_prompt_.value());
  newConversation->setTemperature(temperature_.value());
  newConversation->setMaxTokens(max_tokens_.value());
  newConversation->setSettings(settings_.value());

  return newConversation;
}

// QQmlListProperty回调函数实现
qsizetype Conversation::messages_count(QQmlListProperty<Message> *list) {
  Conversation *conversation = qobject_cast<Conversation *>(list->object);
  return conversation ? conversation->messages_.size() : 0;
}

Message *Conversation::messages_at(QQmlListProperty<Message> *list,
                                   qsizetype index) {
  Conversation *conversation = qobject_cast<Conversation *>(list->object);
  if (conversation && index >= 0 && index < conversation->messages_.size()) {
    return conversation->messages_.at(index);
  }
  return nullptr;
}

void Conversation::messages_append(QQmlListProperty<Message> *list,
                                   Message *message) {
  Conversation *conversation = qobject_cast<Conversation *>(list->object);
  if (conversation) {
    conversation->addMessage(message);
  }
}

void Conversation::messages_clear(QQmlListProperty<Message> *list) {
  Conversation *conversation = qobject_cast<Conversation *>(list->object);
  if (conversation) {
    conversation->clearMessages();
  }
}

} // namespace ChamberUI::Models