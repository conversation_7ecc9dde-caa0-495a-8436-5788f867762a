#pragma once

#include <QBindable>
#include <QDateTime>
#include <QObject>
#include <QProperty>
#include <QQmlListProperty>
#include <QString>
#include <QVariantMap>
#include <gsl/pointers>
#include <qqmlregistration.h>

namespace ChamberUI::Models {

class Message;

class Conversation : public QObject {
  Q_OBJECT
  QML_ELEMENT

  Q_PROPERTY(QString id READ id WRITE setId BINDABLE bindableId)
  Q_PROPERTY(QString title READ title WRITE setTitle BINDABLE bindableTitle)
  Q_PROPERTY(QDateTime createdAt READ createdAt WRITE setCreatedAt BINDABLE
                 bindableCreatedAt)
  Q_PROPERTY(QDateTime updatedAt READ updatedAt WRITE setUpdatedAt BINDABLE
                 bindableUpdatedAt)
  Q_PROPERTY(QString provider READ provider WRITE setProvider BINDABLE
                 bindableProvider)
  Q_PROPERTY(QString model READ model WRITE setModel BINDABLE bindableModel)
  Q_PROPERTY(QString systemPrompt READ systemPrompt WRITE setSystemPrompt
                 BINDABLE bindableSystemPrompt)
  Q_PROPERTY(double temperature READ temperature WRITE setTemperature BINDABLE
                 bindableTemperature)
  Q_PROPERTY(int maxTokens READ maxTokens WRITE setMaxTokens BINDABLE
                 bindableMaxTokens)
  Q_PROPERTY(int messageCount READ messageCount WRITE setMessageCount BINDABLE
                 bindableMessageCount)
  Q_PROPERTY(QVariantMap settings READ settings WRITE setSettings BINDABLE
                 bindableSettings)
  Q_PROPERTY(bool isTemplate READ isTemplate WRITE setIsTemplate BINDABLE
                 bindableIsTemplate)
  Q_PROPERTY(QString templateName READ templateName WRITE setTemplateName
                 BINDABLE bindableTemplateName)
  Q_PROPERTY(
      QQmlListProperty<Message> messages READ messages NOTIFY messagesChanged)

public:
  // 构造函数
  Conversation(const QString &title, QObject *parent = nullptr);
  Conversation(const QString &id, const QString &title,
               QObject *parent = nullptr);

  // 删除拷贝构造函数和赋值操作符
  Conversation(const Conversation &) = delete;
  Conversation &operator=(const Conversation &) = delete;

  // Getters
  QString id() const { return id_.value(); }
  QString title() const { return title_.value(); }
  QDateTime createdAt() const { return created_at_.value(); }
  QDateTime updatedAt() const { return updated_at_.value(); }
  QString provider() const { return provider_.value(); }
  QString model() const { return model_.value(); }
  QString systemPrompt() const { return system_prompt_.value(); }
  double temperature() const { return temperature_.value(); }
  int maxTokens() const { return max_tokens_.value(); }
  int messageCount() const { return message_count_.value(); }
  QVariantMap settings() const { return settings_.value(); }
  bool isTemplate() const { return is_template_.value(); }
  QString templateName() const { return template_name_.value(); }

  // Setters
  void setId(const QString &id) { id_ = id; }
  void setTitle(const QString &title) {
    title_ = title;
    updateTimestamp();
  }
  void setCreatedAt(const QDateTime &createdAt) { created_at_ = createdAt; }
  void setUpdatedAt(const QDateTime &updatedAt) { updated_at_ = updatedAt; }
  void setProvider(const QString &provider) {
    provider_ = provider;
    updateTimestamp();
  }
  void setModel(const QString &model) {
    model_ = model;
    updateTimestamp();
  }
  void setSystemPrompt(const QString &systemPrompt) {
    system_prompt_ = systemPrompt;
    updateTimestamp();
  }
  void setTemperature(double temperature) {
    temperature_ = temperature;
    updateTimestamp();
  }
  void setMaxTokens(int maxTokens) {
    max_tokens_ = maxTokens;
    updateTimestamp();
  }
  void setMessageCount(int messageCount) { message_count_ = messageCount; }
  void setSettings(const QVariantMap &settings) {
    settings_ = settings;
    updateTimestamp();
  }
  void setIsTemplate(bool isTemplate) { is_template_ = isTemplate; }
  void setTemplateName(const QString &templateName) {
    template_name_ = templateName;
  }

  // Bindable getters
  QBindable<QString> bindableId() { return {&id_}; }
  QBindable<QString> bindableTitle() { return {&title_}; }
  QBindable<QDateTime> bindableCreatedAt() { return {&created_at_}; }
  QBindable<QDateTime> bindableUpdatedAt() { return {&updated_at_}; }
  QBindable<QString> bindableProvider() { return {&provider_}; }
  QBindable<QString> bindableModel() { return {&model_}; }
  QBindable<QString> bindableSystemPrompt() { return {&system_prompt_}; }
  QBindable<double> bindableTemperature() { return {&temperature_}; }
  QBindable<int> bindableMaxTokens() { return {&max_tokens_}; }
  QBindable<int> bindableMessageCount() { return {&message_count_}; }
  QBindable<QVariantMap> bindableSettings() { return {&settings_}; }
  QBindable<bool> bindableIsTemplate() { return {&is_template_}; }
  QBindable<QString> bindableTemplateName() { return {&template_name_}; }

  // Messages管理 (QML友好)
  QQmlListProperty<Message> messages();
  Q_INVOKABLE void addMessage(Message *message);
  Q_INVOKABLE void removeMessage(Message *message);
  Q_INVOKABLE void clearMessages();
  Q_INVOKABLE int getMessageCount() const;
  Q_INVOKABLE [[nodiscard]] Message *messageAt(int index) const;
  Q_INVOKABLE [[nodiscard]] Message *lastMessage() const;
  Q_INVOKABLE [[nodiscard]] Message *
  getMessage(const QString &message_id) const;

  // C++友好的messages访问
  const QList<Message *> &getMessages() const { return messages_; }
  void setMessages(const QList<Message *> &messages);

  // 便利方法
  Q_INVOKABLE void updateTimestamp() {
    setUpdatedAt(QDateTime::currentDateTime());
  }

  Q_INVOKABLE bool isValid() const {
    return !id_.value().isEmpty() && !title_.value().isEmpty();
  }

  Q_INVOKABLE void updateMessageCount() { setMessageCount(messages_.size()); }

  // Settings便利方法
  Q_INVOKABLE void setSettingValue(const QString &key, const QVariant &value);
  Q_INVOKABLE QVariant getSettingValue(
      const QString &key, const QVariant &defaultValue = QVariant()) const;

  // 模板相关
  Q_INVOKABLE void markAsTemplate(const QString &templateName);
  Q_INVOKABLE void unmarkAsTemplate();
  Q_INVOKABLE Conversation *createFromTemplate(const QString &newId,
                                               const QString &newTitle) const;

  bool removeMessage(const QString &message_id);
  void addMessagesIfEmpty(const QList<gsl::not_null<Message *>> &messages);
  QList<gsl::not_null<const Message*>> getHistory() const;

signals:
  void messagesChanged();

private:
  // QQmlListProperty回调函数
  static qsizetype messages_count(QQmlListProperty<Message> *list);
  static Message *messages_at(QQmlListProperty<Message> *list, qsizetype index);
  static void messages_append(QQmlListProperty<Message> *list,
                              Message *message);
  static void messages_clear(QQmlListProperty<Message> *list);

private:
  QProperty<QString> id_;
  QProperty<QString> title_;
  QProperty<QDateTime> created_at_;
  QProperty<QDateTime> updated_at_;
  QProperty<QString> provider_;
  QProperty<QString> model_;
  QProperty<QString> system_prompt_;
  QProperty<double> temperature_{0.7};
  QProperty<int> max_tokens_{2048};
  QProperty<int> message_count_{0};
  QProperty<QVariantMap> settings_;
  QProperty<bool> is_template_{false};
  QProperty<QString> template_name_;
  QList<Message *> messages_; // QML使用QQmlListProperty，C++使用这个
};

} // namespace ChamberUI::Models