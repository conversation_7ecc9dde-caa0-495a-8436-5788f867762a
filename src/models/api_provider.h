#pragma once

#include <gsl/gsl>

#include <QBindable>
#include <QDateTime>
#include <QObject>
#include <QProperty>
#include <QString>
#include <qqmlregistration.h>

namespace ChamberUI::Models {

class APIProvider : public QObject {
  Q_OBJECT
  QML_ELEMENT

  Q_PROPERTY(QString provider READ provider WRITE setProvider BINDABLE
                 bindableProvider)
  Q_PROPERTY(QString key READ key WRITE setKey BINDABLE bindableKey)
  Q_PROPERTY(QString apiUrl READ apiUrl WRITE setApiUrl BINDABLE bindableApiUrl)
  Q_PROPERTY(QString model READ model WRITE setModel BINDABLE bindableModel)
  Q_PROPERTY(double temperature READ temperature WRITE setTemperature BINDABLE
                 bindableTemperature)
  Q_PROPERTY(int maxTokens READ maxTokens WRITE setMaxTokens BINDABLE
                 bindableMaxTokens)
  Q_PROPERTY(QDateTime updateAt READ updateAt WRITE setUpdateAt BINDABLE
                 bindableUpdateAt)

public:
  // 构造函数
  explicit APIProvider(gsl::not_null<QObject *> parent);
  APIProvider(const QString &_provider, const QString &_key,
              const QDateTime &_updateAt = QDateTime::currentDateTime(),
              gsl::not_null<QObject *> parent);

  // 删除拷贝构造函数和赋值操作符
  APIProvider(const APIProvider &) = delete;
  APIProvider &operator=(const APIProvider &) = delete;

  // Getters
  QString provider() const { return provider_.value(); }
  QString key() const { return key_.value(); }
  QString apiUrl() const { return api_url_.value(); }
  QString model() const { return model_.value(); }
  double temperature() const { return temperature_.value(); }
  int maxTokens() const { return max_tokens_.value(); }
  QDateTime updateAt() const { return update_at_.value(); }

  // Setters
  void setProvider(const QString &provider) { provider_ = provider; }
  void setKey(const QString &key) { key_ = key; }
  void setApiUrl(const QString &api_url) { api_url_ = api_url; }
  void setModel(const QString &model) { model_ = model; }
  void setTemperature(double temperature) { temperature_ = temperature; }
  void setMaxTokens(int max_tokens) { max_tokens_ = max_tokens; }
  void setUpdateAt(const QDateTime &update_at) { update_at_ = update_at; }

  // Bindable getters
  QBindable<QString> bindableProvider() { return {&provider_}; }
  QBindable<QString> bindableKey() { return {&key_}; }
  QBindable<QString> bindableApiUrl() { return {&api_url_}; }
  QBindable<QString> bindableModel() { return {&model_}; }
  QBindable<double> bindableTemperature() { return {&temperature_}; }
  QBindable<int> bindableMaxTokens() { return {&max_tokens_}; }
  QBindable<QDateTime> bindableUpdateAt() { return {&update_at_}; }

  // 便利方法
  Q_INVOKABLE void updateTimestamp() {
    setUpdateAt(QDateTime::currentDateTime());
  }

  Q_INVOKABLE bool isValid() const {
    return !provider_.value().isEmpty() && !key_.value().isEmpty();
  }

private:
  QProperty<QString> provider_;
  QProperty<QString> key_;
  QProperty<QString> api_url_;
  QProperty<QString> model_;
  QProperty<double> temperature_;
  QProperty<int> max_tokens_;
  QProperty<QDateTime> update_at_;
};

} // namespace ChamberUI::Models