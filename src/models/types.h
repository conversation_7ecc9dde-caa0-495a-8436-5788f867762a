#pragma once

#include <memory>

#include <QList>

namespace ChamberUI::Models {

class Conversation;
class Message;
class Attachment;
class APIProvider;
class ConversationSettings;

using ConversationPtr = std::unique_ptr<Conversation>;
using ConversationListPtr = std::unique_ptr<QList<ConversationPtr>>;
using MessagePtr = std::unique_ptr<Message>;
using MessageListPtr = std::unique_ptr<QList<MessagePtr>>;
using AttachmentPtr = std::unique_ptr<Attachment>;
using AttachmentListPtr = std::unique_ptr<QList<AttachmentPtr>>;
using APIProviderPtr = std::unique_ptr<APIProvider>;

} // namespace ChamberUI::Models
